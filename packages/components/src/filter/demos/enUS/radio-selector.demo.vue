<markdown>
# Radio & Checkbox Selector
</markdown>
<template>
  <z-space vertical>
    <z-filter v-model="filterModel" v-model:data="data" :filters="filterList" />
    <z-data-table :data="data" :columns="columns" />
    <z-code
      language="js"
      word-wrap
      :code="JSON.stringify(filterModel, null, 2)"
    />
  </z-space>
</template>

<script lang="ts">
import { defineComponent, ref, inject } from 'vue'
import { faker } from '@faker-js/faker'
import {
  ZRadio,
  ZRadioGroup,
  ZCheckbox,
  ZCheckboxGroup,
  createFilterItemSelector,
  filterItemInjectionKey
} from '@zeta-gds/components'

// Options
const radioOptions = [
  { label: 'Option A', value: 'A' },
  { label: 'Option B', value: 'B' },
  { label: 'Option C', value: 'C' }
]
const checkboxOptions = [
  { label: 'Item 1', value: '1' },
  { label: 'Item 2', value: '2' },
  { label: 'Item 3', value: '3' }
]

// Data
function createRandomUser () {
  const name = faker.person.firstName()
  const username = faker.internet.userName({ firstName: name })
  return {
    name,
    username,
    radio: radioOptions[Math.floor(Math.random() * radioOptions.length)].value,
    checkbox: [
      checkboxOptions[Math.floor(Math.random() * checkboxOptions.length)].value
    ]
  }
}
const data = ref([
  ...Array(10)
    .fill(0)
    .map(() => createRandomUser())
])

// Filters
const filterList = [
  {
    field: 'radio',
    label: 'Radio',
    selectors: {
      'radio-selector': {
        options: radioOptions,
        value: radioOptions[0].value
      }
    }
  },
  {
    field: 'checkbox',
    label: 'Checkbox',
    selectors: {
      'checkbox-selector': {
        options: checkboxOptions,
        value: []
      }
    }
  }
]

// Custom selectors
createFilterItemSelector('radio-selector', {
  label: 'Radio Selector',
  component: {
    props: {
      value: [String, Number]
    },
    components: {
      ZRadioGroup,
      ZRadio
    },
    setup (props) {
      const Filter = inject(filterItemInjectionKey, null)
      const value = ref(props.value)
      return {
        radioOptions,
        value,
        handler (val) {
          value.value = val
          Filter?.updateValue(val)
        }
      }
    },
    template: `
      <div style="padding:16px; width:300px">
        <z-radio-group v-model="value" @update:modelValue="handler">
          <z-radio v-for="option in radioOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </z-radio>
        </z-radio-group>
      </div>
    `
  },
  filterFn: (value, model, row) => {
    if (!row) return false
    return row.radio === model.value
  }
})

createFilterItemSelector('checkbox-selector', {
  label: 'Checkbox Selector',
  component: {
    props: {
      value: Array
    },
    components: {
      ZCheckboxGroup,
      ZCheckbox
    },
    setup (props) {
      const Filter = inject(filterItemInjectionKey, null)
      const value = ref(props.value || [])
      return {
        checkboxOptions,
        value,
        handler (val) {
          value.value = val
          Filter?.updateValue(val)
        }
      }
    },
    template: `
      <div style="padding:16px; width:300px">
        <z-checkbox-group v-model="value" @update:modelValue="handler">
          <z-checkbox v-for="option in checkboxOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </z-checkbox>
        </z-checkbox-group>
      </div>
    `
  },
  filterFn: (name, model, value) => {
    if (!value?.checkbox) return false
    return model.value.some((v) => value.checkbox.includes(v))
  }
})

export default defineComponent({
  setup () {
    const filterModel = ref({})
    return {
      filterList,
      data,
      filterModel,
      columns: [
        { key: 'name', title: 'Name' },
        { key: 'username', title: 'Username' },
        { key: 'radio', title: 'Radio Value' },
        {
          key: 'checkbox',
          title: 'Checkbox Value',
          render: (row: any) => row.checkbox?.join(', ')
        }
      ]
    }
  }
})
</script>
