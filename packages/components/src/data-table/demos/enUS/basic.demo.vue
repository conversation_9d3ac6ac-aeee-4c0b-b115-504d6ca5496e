<markdown>
# Basic
</markdown>

<template>
  <z-data-table
    :columns="columns"
    :data="data"
    :pagination="pagination"
    :bordered="false"
  />
</template>

<script lang="ts">
import { h, defineComponent } from 'vue'
import {
  ZButton,
  useMessage,
  DataTableColumns,
  ZIcon
} from '@zeta-gds/components'
import {
  PersonCircleOutline as UserIcon,
  Pencil as EditIcon,
  LogOutOutline as LogoutIcon
} from '@vicons/ionicons5'
type Song = {
  no: number
  title: string
  length: string
}

const renderIcon = (icon: any) => () =>
  h(ZIcon, null, {
    default: () => h(icon)
  })
const createColumns = ({
  play
}: {
  play: (row: Song) => void
}): DataTableColumns<Song> => {
  return [
    {
      title: 'No',
      description: 'Select rows for batch operations',
      key: 'no'
    },
    {
      title: 'Title',
      key: 'title',
      ellipsis: true,
      description: 'Select rows for batch operations',
      icon: renderIcon(UserIcon)
    },
    {
      title: 'Length',
      key: 'length',
      ellipsis: true,
      description:
        'Select rows for batch operations to see if ellipse will be present',
      icon: renderIcon(EditIcon)
    },
    {
      title: 'Action',
      key: 'actions',
      description: 'Select rows for batch operations',
      icon: renderIcon(LogoutIcon),
      render (row) {
        return h(
          ZButton,
          {
            strong: true,
            variant: 'light',
            size: 'small',
            onClick: () => play(row)
          },
          { default: () => 'Play' }
        )
      }
    }
  ]
}

const data: Song[] = [
  { no: 3, title: 'Wonderwall', length: '4:18' },
  { no: 4, title: "Don't Look Back in Anger", length: '4:48' },
  { no: 12, title: 'Champagne Supernova', length: '7:27' }
]

export default defineComponent({
  setup () {
    const message = useMessage()
    return {
      data,
      columns: createColumns({
        play (row: Song) {
          message.info(`Play ${row.title}`)
        }
      }),
      pagination: false as const
    }
  }
})
</script>
